module.exports = {
    success: true,
    code: 200,
    message: '操作成功',
    data: [
        {
            uuid: 'ee5ab8961bcf4871baa7e200a2a7aa2d',
            name: '自定义规则',
            valid: 1,
            gmtCreate: '2024-10-22 09:07:47',
            gmtModify: '2024-10-22 09:08:34',
            creator: 'kongtao.wang',
            operator: 'kongtao.wang',
            displayOrder: 1,
            template: 'common/custom',
            fkDealTypeUuid: 'ZRREJECT',
            customId: 'EE5AB896',
            bizType: 'RULE_SET',
            bizUuid: '5bb2fc14c2094026ba2e2d1f1f8bcbd6',
            exeMode: 'FirstMatch',
            ruleConditionDTO: {
                id: 137521,
                uuid: 'fb577a2795954189a022c9beb9af2d34',
                logicOperator: '&&',
                priority: -1,
                creator: 'kongtao.wang',
                operator: 'kongtao.wang',
                fkRuleUuid: 'ee5ab8961bcf4871baa7e200a2a7aa2d',
                params: '',
                children: [
                    {
                        id: 137522,
                        uuid: 'cb60d79319844a68af5d82c61a31223c',
                        parentUuid: 'fb577a2795954189a022c9beb9af2d34',
                        logicOperator: '&&',
                        property: 'pattern/ruleSetResult',
                        op: '==',
                        value: '1',
                        type: 'alias',
                        propertyDataType: '',
                        priority: 1,
                        creator: 'kongtao.wang',
                        operator: 'kongtao.wang',
                        description: '该规则只能用于使用规则流的规则集中，使用本规则执行前的规则集结果进行决策',
                        fkRuleUuid: 'ee5ab8961bcf4871baa7e200a2a7aa2d',
                        params: '[{"name":"conditionOperator","type":"string","value":"=="},{"name":"ruleSet","type":"string","value":"cbdd587c15e441f4a35bdd05f2bf8cd0"},{"name":"conditionValue","type":"string","value":"ZRREJECT"}]',
                        iterateType: 'any',
                        gmtCreate: '2024-10-22 09:07:47',
                        gmtModify: '2024-10-22 09:07:47'
                    }
                ],
                iterateType: 'any',
                gmtCreate: '2024-10-22 09:07:47',
                gmtModify: '2024-10-22 09:07:47'
            },
            originUuid: 'ee5ab8961bcf4871baa7e200a2a7aa2d',
            isDeleted: false,
            termination: 0
        },
        {
            uuid: '328d0cd7044a441099a273291df60ac2',
            name: 'IF',
            valid: 1,
            gmtCreate: '2024-10-22 09:10:09',
            gmtModify: '2024-10-22 09:11:45',
            creator: 'kongtao.wang',
            operator: 'kongtao.wang',
            displayOrder: 2,
            ifClause: 'IF',
            template: 'common/custom',
            fkDealTypeUuid: 'Accept',
            customId: '328D0CD7',
            bizType: 'RULE_SET',
            bizUuid: '5bb2fc14c2094026ba2e2d1f1f8bcbd6',
            description: 'test_rule_1',
            exeMode: 'FirstMatch',
            children: [
                {
                    uuid: 'f09b86b12d2d44d5a7d74594fd6e448f',
                    name: 'Terminated',
                    valid: 0,
                    gmtCreate: '2024-10-22 17:21:47',
                    gmtModify: '2024-10-22 17:21:47',
                    creator: 'zzf',
                    operator: 'zzf',
                    displayOrder: 1,
                    parentUuid: '328d0cd7044a441099a273291df60ac2',
                    template: 'pattern/terminated',
                    fkDealTypeUuid: 'Accept',
                    customId: 'F09B86B1',
                    bizType: 'RULE_SET',
                    bizUuid: '5bb2fc14c2094026ba2e2d1f1f8bcbd6',
                    description: '3',
                    exeMode: 'FirstMatch',
                    ruleConditionDTO: {
                        id: 137543,
                        uuid: 'b6b7560c327e431d8c76d83de4353ede',
                        logicOperator: '&&',
                        op: '',
                        value: '',
                        type: 'context',
                        propertyDataType: '',
                        priority: -1,
                        creator: 'zzf',
                        operator: 'zzf',
                        description: '',
                        fkRuleUuid: 'f09b86b12d2d44d5a7d74594fd6e448f',
                        params: '',
                        children: [
                            {
                                id: 137544,
                                uuid: '35610649bcf7459ebb63ec0af1d65096',
                                parentUuid: 'b6b7560c327e431d8c76d83de4353ede',
                                logicOperator: '&&',
                                property: 'pattern/terminated',
                                op: '==',
                                value: '1',
                                type: 'alias',
                                propertyDataType: '',
                                priority: 1,
                                creator: 'zzf',
                                operator: 'zzf',
                                description:
                                    '在满足if规则的条件下，执行中断规则，则后边的规则都将不执行。Terminated与IF绑定使用，单独使用无效。',
                                fkRuleUuid: 'f09b86b12d2d44d5a7d74594fd6e448f',
                                params: '',
                                iterateType: 'any',
                                gmtCreate: '2024-10-22 17:21:47',
                                gmtModify: '2024-10-22 17:21:47'
                            }
                        ],
                        iterateType: 'any',
                        gmtCreate: '2024-10-22 17:21:47',
                        gmtModify: '2024-10-22 17:21:47'
                    },
                    originUuid: 'f09b86b12d2d44d5a7d74594fd6e448f',
                    isDeleted: false,
                    termination: 0
                }
            ],
            ruleConditionDTO: {
                id: 137526,
                uuid: 'cd1d8db29ef3485ea806903e3482ce55',
                logicOperator: '||',
                priority: -1,
                creator: 'kongtao.wang',
                operator: 'kongtao.wang',
                fkRuleUuid: '328d0cd7044a441099a273291df60ac2',
                params: '',
                children: [
                    {
                        id: 137527,
                        uuid: '9947fb48d5874540bd2f008427f9f75b',
                        parentUuid: 'cd1d8db29ef3485ea806903e3482ce55',
                        logicOperator: '&&',
                        property: 'anomaly/device',
                        op: '==',
                        value: '1',
                        type: 'alias',
                        propertyDataType: '',
                        priority: 1,
                        creator: 'kongtao.wang',
                        operator: 'kongtao.wang',
                        description: '在集成同盾设备指纹技术前提下, 因环境异常导致生成的设备ID为空',
                        fkRuleUuid: '328d0cd7044a441099a273291df60ac2',
                        params: '',
                        iterateType: 'any',
                        gmtCreate: '2024-10-22 09:10:16',
                        gmtModify: '2024-10-22 09:10:16'
                    },
                    {
                        id: 137528,
                        uuid: '4bbd8f502a5c4c8d9dee966f8dc49c19',
                        parentUuid: 'cd1d8db29ef3485ea806903e3482ce55',
                        logicOperator: '&&',
                        property: 'match/addressMatch',
                        op: '==',
                        value: '1',
                        type: 'alias',
                        propertyDataType: '',
                        priority: 2,
                        creator: 'kongtao.wang',
                        operator: 'kongtao.wang',
                        description: '地址两两匹配',
                        fkRuleUuid: '328d0cd7044a441099a273291df60ac2',
                        params: '[{"name":"addressA","type":"string","value":"C_S_CMT"},{"name":"addressB","type":"string","value":"C_S_CMAASDF"},{"name":"similarity","type":"string","value":"50"}]',
                        iterateType: 'any',
                        gmtCreate: '2024-10-22 09:10:16',
                        gmtModify: '2024-10-22 09:10:16'
                    }
                ],
                iterateType: 'any',
                gmtCreate: '2024-10-22 09:10:16',
                gmtModify: '2024-10-22 09:10:16'
            },
            originUuid: '328d0cd7044a441099a273291df60ac2',
            isDeleted: false,
            termination: 0
        },
        {
            uuid: 'edf2da18e8a14869aaecde18c2089b73',
            name: '最近7天累计交易次数',
            valid: 1,
            gmtCreate: '2024-10-22 11:02:26',
            gmtModify: '2024-10-22 11:02:32',
            creator: 'hao.chen',
            operator: 'hao.chen',
            displayOrder: 3,
            template: 'common/custom',
            fkDealTypeUuid: 'ZRREJECT',
            customId: 'EDF2DA18',
            bizType: 'RULE_SET',
            bizUuid: '5bb2fc14c2094026ba2e2d1f1f8bcbd6',
            exeMode: 'FirstMatch',
            ruleConditionDTO: {
                id: 137534,
                uuid: 'fed3a0ccc41441839130af549c36b101',
                logicOperator: '&&',
                priority: -1,
                creator: 'hao.chen',
                operator: 'hao.chen',
                fkRuleUuid: 'edf2da18e8a14869aaecde18c2089b73',
                params: '',
                children: [
                    {
                        id: 137535,
                        uuid: '7a7f4fa26cb944c3a66883b5d182ae11',
                        parentUuid: 'fed3a0ccc41441839130af549c36b101',
                        property: 'salaxyzb_s2kowhfpal',
                        op: '>=',
                        value: '32',
                        type: 'gaea_indicatrix',
                        propertyDataType: 'INT',
                        rightValueType: 'input',
                        priority: 1,
                        creator: 'hao.chen',
                        operator: 'hao.chen',
                        fkRuleUuid: 'edf2da18e8a14869aaecde18c2089b73',
                        params: '',
                        iterateType: 'any',
                        gmtCreate: '2024-10-22 11:02:26',
                        gmtModify: '2024-10-22 11:02:26'
                    }
                ],
                iterateType: 'any',
                gmtCreate: '2024-10-22 11:02:26',
                gmtModify: '2024-10-22 11:02:26'
            },
            originUuid: 'edf2da18e8a14869aaecde18c2089b73',
            isDeleted: false,
            termination: 0
        }
    ]
};
