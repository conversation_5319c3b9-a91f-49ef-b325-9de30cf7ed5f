module.exports = {
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "uuid": "4b3c3aab921c489d8ddf1f968f2fbdd6",
      "name": "循环规则",
      "valid": 0,
      "gmtCreate": "2024-12-04 10:36:21",
      "gmtModify": "2024-12-04 10:36:21",
      "creator": "xyz",
      "operator": "xyz",
      "displayOrder": 1,
      "template": "statement/loop",
      "fkDealTypeUuid": "Accept",
      "customId": "804D1C92",
      "script": "[\n    {\n        \"id\": 1,\n        \"type\": \"loop\",\n        \"params\": {\n            \"loopObject\": {\n                \"fieldName\": \"C_O_CMXH1\",\n                \"dataType\": \"ARRAY\"\n            },\n            \"startStatement\": [],\n            \"loopStatement\": [\n                {\n                    \"id\": 0,\n                    \"type\": \"ifThen\",\n                    \"params\": {\n                        \"condition\": {\n                            \"id\": 1,\n                            \"type\": \"condition\",\n                            \"params\": {\n                                \"logicOperator\": \"&&\",\n                                \"type\": \"context\",\n                                \"children\": [\n                                    {\n                                        \"property\": \"S_S_CUSTCRTDAT\",\n                                        \"op\": \"==\",\n                                        \"value\": \"222\",\n                                        \"propertyDataType\": \"STRING\",\n                                        \"type\": \"context\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 1\n                                    },\n                                    {\n                                        \"property\": \"C_F_XIAOSHUA\",\n                                        \"op\": \">\",\n                                        \"value\": \"11\",\n                                        \"propertyDataType\": \"DOUBLE\",\n                                        \"type\": \"context\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 2\n                                    },\n                                    {\n                                        \"property\": \"S_N_WEEKLYTIME\",\n                                        \"op\": \"<\",\n                                        \"value\": \"100\",\n                                        \"propertyDataType\": \"INT\",\n                                        \"type\": \"context\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 3\n                                    },\n                                    {\n                                        \"property\": \"S_B_ABNORMALTAXACT\",\n                                        \"op\": \"notnull\",\n                                        \"propertyDataType\": \"BOOLEAN\",\n                                        \"type\": \"context\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 4\n                                    },\n                                    {\n                                        \"property\": \"S_D_BRCHCLOSETIME\",\n                                        \"op\": \"notnull\",\n                                        \"propertyDataType\": \"DATETIME\",\n                                        \"type\": \"context\",\n                                        \"rightValueType\": \"context\",\n                                        \"priority\": 5\n                                    },\n                                    {\n                                        \"property\": \"C_O_SHOW1.char\",\n                                        \"op\": \"==\",\n                                        \"value\": \"aaa\",\n                                        \"propertyDataType\": \"STRING\",\n                                        \"type\": \"context\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 6\n                                    },\n                                    {\n                                        \"property\": \"C_F_CMSFCFC1\",\n                                        \"op\": \"==\",\n                                        \"value\": \"888\",\n                                        \"propertyDataType\": \"DOUBLE\",\n                                        \"type\": \"context\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 7\n                                    },\n                                    {\n                                        \"property\": \"component.function_F7471963964.C_F_XIAOSHUC\",\n                                        \"op\": \">\",\n                                        \"value\": \"10\",\n                                        \"propertyDataType\": \"DOUBLE\",\n                                        \"type\": \"component\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 8\n                                    },\n                                    {\n                                        \"property\": \"component.function_S6953596485.C_S_CAMPAIGNNAME\",\n                                        \"op\": \"==\",\n                                        \"value\": \"cm_test10\",\n                                        \"propertyDataType\": \"STRING\",\n                                        \"type\": \"component\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 9\n                                    },\n                                    {\n                                        \"property\": \"salaxyzb_vp4hp7v8iw\",\n                                        \"op\": \">\",\n                                        \"value\": \"0\",\n                                        \"propertyDataType\": \"INT\",\n                                        \"type\": \"gaea_indicatrix\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 10\n                                    },\n                                    {\n                                        \"property\": \"C_S_CMSFCFC3\",\n                                        \"op\": \"==\",\n                                        \"value\": \"cm脚本字段\",\n                                        \"propertyDataType\": \"STRING\",\n                                        \"type\": \"context\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 11\n                                    },\n                                    {\n                                        \"property\": \"tianzuo_case9999\",\n                                        \"op\": \"==\",\n                                        \"value\": \"22\",\n                                        \"propertyDataType\": \"STRING\",\n                                        \"type\": \"context\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 12\n                                    },\n                                    {\n                                        \"property\": \"derivedzb_ft4o1oau6l\",\n                                        \"op\": \">\",\n                                        \"value\": \"1\",\n                                        \"propertyDataType\": \"INT\",\n                                        \"type\": \"context\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 13\n                                    },\n                                    {\n                                        \"property\": \"yuntuoffline_wfo5l2dtr2\",\n                                        \"op\": \"notnull\",\n                                        \"propertyDataType\": \"DOUBLE\",\n                                        \"type\": \"context\",\n                                        \"rightValueType\": \"context\",\n                                        \"priority\": 14\n                                    },\n                                    {\n                                        \"property\": \"offlinezb_p4nhsrnneq\",\n                                        \"op\": \"==\",\n                                        \"value\": \"0\",\n                                        \"propertyDataType\": \"INT\",\n                                        \"type\": \"gaea_indicatrix\",\n                                        \"rightValueType\": \"input\",\n                                        \"priority\": 15\n                                    }\n                                ]\n                            }\n                        },\n                        \"otherwise\": [\n                            {\n                                \"id\": 0,\n                                \"type\": \"dealType\",\n                                \"params\": {\n                                    \"dealType\": \"Review\"\n                                }\n                            }\n                        ],\n                        \"then\": [\n                            {\n                                \"id\": 0,\n                                \"type\": \"dealType\",\n                                \"params\": {\n                                    \"dealType\": \"ZRREJECT\"\n                                }\n                            }\n                        ]\n                    }\n                }\n            ],\n            \"endStatement\": []\n        }\n    }\n]",
      "bizType": "RULE_SET",
      "bizUuid": "876e841d4532422397fa3ea1b1a0e769",
      "exeMode": "WorstMatch",
      "ruleConditionDTO": {
        "id": 12790,
        "uuid": "bd0e465accfb483e9db036f649bccca8",
        "priority": -1,
        "creator": "cm",
        "operator": "cm",
        "fkRuleUuid": "4b3c3aab921c489d8ddf1f968f2fbdd6",
        "params": "",
        "iterateType": "any",
        "gmtCreate": "2024-12-04 10:36:21",
        "gmtModify": "2024-12-04 10:36:21"
      },
      "originUuid": "804d1c92a5ba49d290ab3cd01c09d646",
      "isDeleted": false,
      "termination": 0
    },
    {
      "uuid": "38b5933aba104b119ca679b372f48b84",
      "name": "循环规则2",
      "valid": 1,
      "gmtCreate": "2024-12-04 10:36:21",
      "gmtModify": "2024-12-04 10:36:21",
      "creator": "xyz",
      "operator": "xyz",
      "displayOrder": 2,
      "template": "statement/loop",
      "fkDealTypeUuid": "Accept",
      "customId": "03DE32B5",
      "script": "[\n    {\n        \"id\": 1,\n        \"type\": \"loop\",\n        \"params\": {\n            \"loopObject\": {\n                \"fieldName\": \"C_O_CMDXH1\",\n                \"dataType\": \"ARRAY\"\n            },\n            \"startStatement\": [],\n            \"loopStatement\": [\n                {\n                    \"id\": 0,\n                    \"type\": \"loop\",\n                    \"params\": {\n                        \"loopObject\": {\n                            \"fieldName\": \"C_O_CMDXH1.C_O_CMXH1\",\n                            \"dataType\": \"ARRAY.ARRAY\"\n                        },\n                        \"startStatement\": [],\n                        \"loopStatement\": [\n                            {\n                                \"id\": 0,\n                                \"type\": \"ifThen\",\n                                \"params\": {\n                                    \"condition\": {\n                                        \"id\": 1,\n                                        \"type\": \"condition\",\n                                        \"params\": {\n                                            \"logicOperator\": \"&&\",\n                                            \"type\": \"context\",\n                                            \"children\": [\n                                                {\n                                                    \"logicOperator\": \"&&\",\n                                                    \"type\": \"context\",\n                                                    \"children\": [\n                                                        {\n                                                            \"property\": \"C_O_CMDXH1.C_O_CMXH1.name\",\n                                                            \"op\": \"==\",\n                                                            \"propertyDataType\": \"ARRAY.ARRAY.STRING\",\n                                                            \"type\": \"object_context\",\n                                                            \"rightValueType\": \"input\",\n                                                            \"priority\": 2,\n                                                            \"value\": \"cm\"\n                                                        },\n                                                        {\n                                                            \"property\": \"C_O_CMDXH1.C_O_CMXH1.age\",\n                                                            \"propertyDataType\": \"ARRAY.ARRAY.INT\",\n                                                            \"op\": \"==\",\n                                                            \"value\": \"11\",\n                                                            \"rightValueType\": \"input\",\n                                                            \"priority\": 3,\n                                                            \"type\": \"object_context\"\n                                                        }\n                                                    ],\n                                                    \"priority\": 1\n                                                },\n                                                {\n                                                    \"property\": \"S_S_CUSTCRTDAT\",\n                                                    \"op\": \"==\",\n                                                    \"value\": \"222\",\n                                                    \"propertyDataType\": \"STRING\",\n                                                    \"type\": \"context\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 4\n                                                },\n                                                {\n                                                    \"property\": \"C_F_XIAOSHUA\",\n                                                    \"op\": \">\",\n                                                    \"value\": \"11\",\n                                                    \"propertyDataType\": \"DOUBLE\",\n                                                    \"type\": \"context\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 5\n                                                },\n                                                {\n                                                    \"property\": \"S_N_WEEKLYTIME\",\n                                                    \"op\": \"<\",\n                                                    \"value\": \"100\",\n                                                    \"propertyDataType\": \"INT\",\n                                                    \"type\": \"context\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 6\n                                                },\n                                                {\n                                                    \"property\": \"S_B_ABNORMALTAXACT\",\n                                                    \"op\": \"notnull\",\n                                                    \"propertyDataType\": \"BOOLEAN\",\n                                                    \"type\": \"context\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 7\n                                                },\n                                                {\n                                                    \"property\": \"S_D_BRCHCLOSETIME\",\n                                                    \"op\": \"notnull\",\n                                                    \"propertyDataType\": \"DATETIME\",\n                                                    \"type\": \"context\",\n                                                    \"rightValueType\": \"context\",\n                                                    \"priority\": 8\n                                                },\n                                                {\n                                                    \"property\": \"C_O_SHOW1.char\",\n                                                    \"op\": \"==\",\n                                                    \"value\": \"aaa\",\n                                                    \"propertyDataType\": \"STRING\",\n                                                    \"type\": \"context\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 9\n                                                },\n                                                {\n                                                    \"property\": \"C_F_CMSFCFC1\",\n                                                    \"op\": \"==\",\n                                                    \"value\": \"888\",\n                                                    \"propertyDataType\": \"DOUBLE\",\n                                                    \"type\": \"context\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 10\n                                                },\n                                                {\n                                                    \"property\": \"component.function_F7471963964.C_F_XIAOSHUC\",\n                                                    \"op\": \">\",\n                                                    \"value\": \"10\",\n                                                    \"propertyDataType\": \"DOUBLE\",\n                                                    \"type\": \"component\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 11\n                                                },\n                                                {\n                                                    \"property\": \"component.function_S6953596485.C_S_CAMPAIGNNAME\",\n                                                    \"op\": \"==\",\n                                                    \"value\": \"cm_test10\",\n                                                    \"propertyDataType\": \"STRING\",\n                                                    \"type\": \"component\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 12\n                                                },\n                                                {\n                                                    \"property\": \"salaxyzb_vp4hp7v8iw\",\n                                                    \"op\": \">\",\n                                                    \"value\": \"0\",\n                                                    \"propertyDataType\": \"INT\",\n                                                    \"type\": \"gaea_indicatrix\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 13\n                                                },\n                                                {\n                                                    \"property\": \"C_S_CMSFCFC3\",\n                                                    \"op\": \"==\",\n                                                    \"value\": \"cm脚本字段\",\n                                                    \"propertyDataType\": \"STRING\",\n                                                    \"type\": \"context\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 14\n                                                },\n                                                {\n                                                    \"property\": \"derivedzb_ft4o1oau6l\",\n                                                    \"op\": \">\",\n                                                    \"value\": \"1\",\n                                                    \"propertyDataType\": \"INT\",\n                                                    \"type\": \"context\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 15\n                                                },\n                                                {\n                                                    \"property\": \"yuntuoffline_wfo5l2dtr2\",\n                                                    \"op\": \"notnull\",\n                                                    \"propertyDataType\": \"DOUBLE\",\n                                                    \"type\": \"context\",\n                                                    \"rightValueType\": \"context\",\n                                                    \"priority\": 16\n                                                },\n                                                {\n                                                    \"property\": \"offlinezb_p4nhsrnneq\",\n                                                    \"op\": \"==\",\n                                                    \"value\": \"0\",\n                                                    \"propertyDataType\": \"INT\",\n                                                    \"type\": \"gaea_indicatrix\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 17\n                                                }\n                                            ]\n                                        }\n                                    },\n                                    \"otherwise\": [\n                                        {\n                                            \"id\": 0,\n                                            \"type\": \"dealType\",\n                                            \"params\": {\n                                                \"dealType\": \"Reject\"\n                                            }\n                                        }\n                                    ],\n                                    \"then\": [\n                                        {\n                                            \"id\": 0,\n                                            \"type\": \"dealType\",\n                                            \"params\": {\n                                                \"dealType\": \"suspicious\"\n                                            }\n                                        }\n                                    ]\n                                }\n                            }\n                        ],\n                        \"endStatement\": []\n                    }\n                }\n            ],\n            \"endStatement\": []\n        }\n    }\n]",
      "bizType": "RULE_SET",
      "bizUuid": "876e841d4532422397fa3ea1b1a0e769",
      "exeMode": "WorstMatch",
      "ruleConditionDTO": {
        "id": 12791,
        "uuid": "92bc4e420feb40a08e4d16767e8c1124",
        "priority": -1,
        "creator": "cty",
        "operator": "cty",
        "fkRuleUuid": "38b5933aba104b119ca679b372f48b84",
        "params": "",
        "iterateType": "any",
        "gmtCreate": "2024-12-04 10:36:21",
        "gmtModify": "2024-12-04 10:36:21"
      },
      "originUuid": "03de32b5cf7f40bebef0186c3de03c01",
      "isDeleted": false,
      "termination": 0
    },
    {
      "uuid": "d38cebcc1f694a50b1adb273923cfa95",
      "name": "循环规则2_副本",
      "valid": 0,
      "gmtCreate": "2024-12-04 10:36:21",
      "gmtModify": "2024-12-04 10:36:21",
      "creator": "xyz",
      "operator": "xyz",
      "displayOrder": 3,
      "template": "statement/loop",
      "fkDealTypeUuid": "Accept",
      "customId": "02C903B4",
      "script": "[\n    {\n        \"id\": 1,\n        \"type\": \"loop\",\n        \"params\": {\n            \"loopObject\": {\n                \"fieldName\": \"C_O_CMDXH1\",\n                \"dataType\": \"ARRAY\"\n            },\n            \"startStatement\": [],\n            \"loopStatement\": [\n                {\n                    \"id\": 0,\n                    \"type\": \"loop\",\n                    \"params\": {\n                        \"loopObject\": {\n                            \"fieldName\": \"C_O_CMDXH1.C_O_CMXH1\",\n                            \"dataType\": \"ARRAY.ARRAY\"\n                        },\n                        \"startStatement\": [],\n                        \"loopStatement\": [\n                            {\n                                \"id\": 0,\n                                \"type\": \"ifThen\",\n                                \"params\": {\n                                    \"condition\": {\n                                        \"id\": 1,\n                                        \"type\": \"condition\",\n                                        \"params\": {\n                                            \"logicOperator\": \"&&\",\n                                            \"type\": \"context\",\n                                            \"children\": [\n                                                {\n                                                    \"logicOperator\": \"&&\",\n                                                    \"type\": \"context\",\n                                                    \"children\": [\n                                                        {\n                                                            \"property\": \"C_O_CMDXH1.C_O_CMXH1.name\",\n                                                            \"op\": \"==\",\n                                                            \"propertyDataType\": \"ARRAY.ARRAY.STRING\",\n                                                            \"type\": \"object_context\",\n                                                            \"rightValueType\": \"input\",\n                                                            \"priority\": 2,\n                                                            \"value\": \"cm\"\n                                                        },\n                                                        {\n                                                            \"property\": \"C_O_CMDXH1.C_O_CMXH1.age\",\n                                                            \"propertyDataType\": \"ARRAY.ARRAY.INT\",\n                                                            \"op\": \"==\",\n                                                            \"value\": \"11\",\n                                                            \"rightValueType\": \"input\",\n                                                            \"priority\": 3,\n                                                            \"type\": \"object_context\"\n                                                        }\n                                                    ],\n                                                    \"priority\": 1\n                                                },\n                                                {\n                                                    \"property\": \"C_O_SHOW1.char\",\n                                                    \"op\": \"==\",\n                                                    \"value\": \"aaa\",\n                                                    \"propertyDataType\": \"STRING\",\n                                                    \"type\": \"context\",\n                                                    \"rightValueType\": \"input\",\n                                                    \"priority\": 4\n                                                }\n                                            ]\n                                        }\n                                    },\n                                    \"otherwise\": [\n                                        {\n                                            \"id\": 0,\n                                            \"type\": \"dealType\",\n                                            \"params\": {\n                                                \"dealType\": \"Reject\"\n                                            }\n                                        }\n                                    ],\n                                    \"then\": [\n                                        {\n                                            \"id\": 0,\n                                            \"type\": \"dealType\",\n                                            \"params\": {\n                                                \"dealType\": \"suspicious\"\n                                            }\n                                        }\n                                    ]\n                                }\n                            }\n                        ],\n                        \"endStatement\": []\n                    }\n                }\n            ],\n            \"endStatement\": []\n        }\n    }\n]",
      "bizType": "RULE_SET",
      "bizUuid": "876e841d4532422397fa3ea1b1a0e769",
      "exeMode": "WorstMatch",
      "ruleConditionDTO": {
        "id": 12792,
        "uuid": "516e71a3a57d498bb610b1a87088ccab",
        "priority": -1,
        "creator": "xyz",
        "operator": "xyz",
        "fkRuleUuid": "d38cebcc1f694a50b1adb273923cfa95",
        "params": "",
        "iterateType": "any",
        "gmtCreate": "2024-12-04 10:36:21",
        "gmtModify": "2024-12-04 10:36:21"
      },
      "originUuid": "02c903b4df314dfb971ecaf0c0c9b4cd",
      "isDeleted": false,
      "termination": 0
    }
  ]
}