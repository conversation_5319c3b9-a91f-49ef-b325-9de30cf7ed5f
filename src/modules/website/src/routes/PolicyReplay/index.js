import { useRef } from 'react';
import PolicyReplay from '~modules/Report/PolicyReplay';

export default () => {
    const reportRef = useRef();
    // 1702292450498808A2EF6XCMKQI8MX7FWBH
    //169985866000080BD6674XNUNIYAQCJV6HI
    // 1702364328000BFF10061PFUZW7MOGDF5F5
    // 1702519351234A3C00060SKTBNYVTU9KEQF

    // 17314905513820AAE0066EGJA1FHNLVLMV6
    const data = { id: '1734861163000A20F005B4J4EO73OWYBEWG', type: '2' };
    //1729755256485082300661VOAQ1W9I6AT4D
    //173017249044007BB00668GLXTSYP6OTM46
    return (
        <div style={{ height: '100vh', overflowY: 'auto' }}>
            <PolicyReplay
                title="测试"
                {...data}
                // subjectTokenId="1735097064000A7880065FCEMG74JMETQ3D"
                allMap={{ ruleAndIndexFieldMap: {}, ruleAndIndexFieldList: [] }}
                ref={reportRef}
            />
        </div>
    );
};
