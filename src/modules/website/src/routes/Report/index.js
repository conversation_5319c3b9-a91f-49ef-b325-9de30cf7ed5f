import { useRef } from 'react';
import Report from '~modules/Report';
import ExportBtn from '~modules/Report/ExportBtn';

export default () => {
    const reportRef = useRef();
    // 1702292450498808A2EF6XCMKQI8MX7FWBH
    //169985866000080BD6674XNUNIYAQCJV6HI
    // 1702364328000BFF10061PFUZW7MOGDF5F5
    // 1702519351234A3C00060SKTBNYVTU9KEQF

    const data = { id: '1733121190979184A0066BE8WZQJWJU378P', type: '2' };
    return (
        <div style={{ height: '100vh', overflowY: 'auto' }}>
            <ExportBtn
                title="测试"
                handleClick={() => {
                    return reportRef?.current || {};
                }}
            />
            <Report
                title="测试"
                {...data}
                showReplayAction={true}
                allMap={{ ruleAndIndexFieldMap: {}, ruleAndIndexFieldList: [] }}
                ref={reportRef}
            />
        </div>
    );
};
