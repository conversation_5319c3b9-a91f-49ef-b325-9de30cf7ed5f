import request from '../utils/request';
import { getHeader } from './common';

/**
 * @description: 统一登录接口
 * @param params
 * @returns {Promise<Object>}
 */
const userLogin = async (params) => {
    return request(
        '/bridgeApi/user/login',
        {
            method: 'POST',
            body: { ...params }
        },
        true
    );
};

// 生成一次性token
const auth = async (params) => {
    return request(
        '/bridgeApi/user/getAuthCode',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};

export default {
    userLogin,
    auth
};
