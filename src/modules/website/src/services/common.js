import queryString from 'query-string';

const getUrl = (url, query) => {
    let currentApp = localStorage.currentApp ? JSON.parse(localStorage.currentApp) : null;
    query = query ? query : {};
    /*
     * @倪春龙
     * 这段代码是为所有post和Put接口添加appName，即当前渠道信息
     * */
    if (!query.appCode && currentApp && currentApp.key) {
        // 判断线上appName为all的情况
        if (currentApp.key === 'all') {
            currentApp.key = '';
        }
        query.appCode = currentApp.key;
        // 不需要appName的接口
        if (query.noAppName) {
            delete query.noAppName;
            delete query.appCode;
        }
    }
    // 不需要appName的接口
    if (query.noAppName) {
        delete query.noAppName;
    }
    return url + '?' + queryString.stringify(query);
};

const pingURL = (url, query) => {
    return url + `${query.keyStr}`;
};

const getHeader = () => {
    let headers = {};
    headers['X-Cf-Random'] = sessionStorage.getItem('_csrf_');
    return headers;
};

const deleteEmptyObjItem = (obj) => {
    for (let i in obj) {
        let value = obj[i];
        if (!value && value !== 0) {
            delete obj[i];
        }
    }
    return obj;
};
export { getUrl, getHeader, deleteEmptyObjItem, pingURL };
