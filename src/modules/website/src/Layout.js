import { useState } from 'react';
import { Row, Menu, DevelopmentLogin, message } from 'tntd';
import userApi from './services/user';
import { rsaPwd } from './utils/user';
import './Layout.less';

const { SubMenu } = Menu;

export default ({ navlist, children, history, routerPrefix }) => {
    const [openKeys, setOpenKeys] = useState();
    const onOpenChange = (curOpenKeys) => {
        setOpenKeys(curOpenKeys);
    };

    const signIn = async (data) => {
        const { account, password } = data;
        const params = { account, password: rsaPwd(password) };
        let { tempRandom } = [''];
        // 获取加盐随机数
        const authResult = await userApi.auth(params);
        tempRandom = authResult.data;

        if (authResult.success && tempRandom) {
            const res = await userApi.userLogin({ ...params, tempRandom });
            if (!res) return;
            if (res.success) {
                const csrfToken = res.data.csrfToken;
                sessionStorage.setItem('_csrf_', csrfToken);
                localStorage.setItem('_sync_qjt_csrf_', csrfToken); // 新的csrf同步到其他页面
                localStorage.setItem('developmentLoginData', JSON.stringify(params));

                location.reload();
            } else {
                message.error(res.message);
                return Promise.reject(res.message);
            }
        } else {
            message.error(authResult.message);
        }
    };

    return (
        <div>
            <div className="mock-login">
                <DevelopmentLogin signIn={signIn} />
            </div>
            <Row type="flex" className="test-layout-wrap">
                <div className="left-nav">
                    <Menu mode="inline" openKeys={openKeys} onOpenChange={onOpenChange}>
                        {Object.keys(navlist).map((menu) => {
                            return (
                                <SubMenu key={menu} title={menu}>
                                    {navlist[menu]?.map((item) => {
                                        return (
                                            <Menu.Item
                                                key={item?.name}
                                                onClick={() => {
                                                    history.push(`${routerPrefix}${item.path}`);
                                                }}>
                                                {item?.name}
                                            </Menu.Item>
                                        );
                                    })}
                                </SubMenu>
                            );
                        })}
                    </Menu>
                </div>
                <div className="content-wrap">{children}</div>
            </Row>
        </div>
    );
};
