import { Router, Route, Switch, Redirect } from 'dva/router';
import dynamic from 'dva/dynamic';
import { Spin } from 'tntd';
import Layout from './Layout';

const routerPrefix = '/modules';

const flatten = (arr) => {
    var res = [];
    for (let i = 0, length = arr.length; i < length; i++) {
        if (Array.isArray(arr[i])) {
            res = res.concat(flatten(arr[i]));
        } else {
            res.push(arr[i]);
        }
    }
    return res;
};

// 设置默认的加载组件
dynamic.setDefaultLoadingComponent(() => {
    return <Spin className="globalSpin" />;
});

// 抽象化菜单配置
const getNavList = (app) => {
    let navList = {};
    // 中台
    navList['中台'] = [
        {
            name: '报告',
            enName: 'Report',
            path: '/report',
            icon: 'home',
            component: dynamic({
                app,
                component: () => import('./routes/Report')
            })
        },
        {
            name: '回放',
            enName: 'Report',
            path: '/policyReplay',
            icon: 'home',
            component: dynamic({
                app,
                component: () => import('./routes/PolicyReplay')
            })
        }
    ];
    // 指标
    navList['极溯'] = [
        {
            name: '实时指标',
            enName: 'Report',
            path: '/report1',
            icon: 'home',
            component: dynamic({
                app,
                component: () => import('~modules/Report')
            })
        }
    ];
    return navList;
};
export default ({ history, app }) => {
    const navlist = Object.values(getNavList(app));
    const normalRoutes = flatten(navlist);
    const navs = [...normalRoutes].map((item) => ({
        ...item,
        path: `${routerPrefix}${item.path}`
    }));
    return (
        <Router history={history}>
            <Layout routerPrefix={routerPrefix} history={history} navlist={getNavList(app)}>
                <Switch>
                    {navs.map(({ exact = false, path, component }) => {
                        return <Route exact={exact} key={path} path={path} component={component} />;
                    })}
                    <Redirect exact from="/" to={`${routerPrefix}/report`} />
                </Switch>
            </Layout>
        </Router>
    );
};
