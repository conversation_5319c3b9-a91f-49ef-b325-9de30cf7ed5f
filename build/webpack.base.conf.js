const path = require('path');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const autoprefixer = require('autoprefixer');
const TransformPathResolvePlugin = require('@tntd/webpack-transform-path-resolve-plugin');

const config = require('./config');
const devMode = process.env.npm_lifecycle_event.includes('start');
const { staticPath, staticSourcePath, version } = config;

const replaceComponents = ['icon', 'checkbox'];

function resolve(dir) {
    return path.join(__dirname, '..', dir);
}

module.exports = {
    cache: true,
    context: path.resolve(__dirname, '../'),
    entry: {
        app: './src/app.js'
    },
    output: {
        path: config.build.assetsRoot,
        filename: staticPath + '/js/[name].[chunkhash:8].js',
        publicPath: '/'
    },
    resolve: {
        extensions: ['.js', '.json'],
        alias: {
            '@': resolve('src'),
            tntd: `tntd-${version}`,
            '~locale': resolve('src/constants/locale'),
            '~modules': resolve('src/modules/Components'),
            '~I18N': resolve('src/modules/I18N.js')
        },
        plugins: [
            new TransformPathResolvePlugin([
                ...replaceComponents.map((name) => ({
                    // antd里面icon使用tntd里的
                    matchPath(sourcePath, modulePath) {
                        return (
                            sourcePath.includes(path.join('node_modules', 'antd')) &&
                            modulePath.endsWith(`../${name}`) &&
                            modulePath !== path.join('tntd', 'es', name)
                        );
                    },
                    transform(modulePath) {
                        return path.join('tntd', 'es', name);
                    }
                })),
                {
                    matchPath(sourcePath, modulePath) {
                        return sourcePath.includes('node_modules') && modulePath.includes('antd') && modulePath.includes('style');
                    },
                    transform(filePath) {
                        if (filePath) {
                            return filePath.replace('antd', 'tntd');
                        }
                    }
                }
            ])
        ]
    },
    plugins: [
        new webpack.DllReferencePlugin({
            context: __dirname,
            manifest: path.resolve(__dirname, '../node_modules/@tntd/dll/dist/vendor-ie/lightbox_new_dll_manifest.json')
        }),
        new webpack.ProvidePlugin({
            React: 'react'
        }),
        new webpack.HotModuleReplacementPlugin()
    ],
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /(node_modules)/,
                include: [
                    resolve('src'),
                    resolve('node_modules/d3-force'),
                    // resolve('node_modules/d3-hierarchy'),
                    resolve('node_modules/@dnd-kit'),
                    resolve('node_modules/@dnd-kit'),
                    resolve('node_modules/@stellaris'),
                    resolve('node_modules/react-draggable'),
                    resolve('node_modules/react-resizable'),
                    resolve('node_modules/split-on-first'),
                    resolve('node_modules/strict-uri-encode'),
                    resolve('node_modules/query-string'),
                    resolve('node_modules/tslib'),
                    resolve('node_modules/ali-react-table')
                ],
                use: {
                    loader: 'babel-loader?cacheDirectory=true'
                }
            },
            {
                test: /\.css$/,
                use: [devMode ? 'style-loader' : MiniCssExtractPlugin.loader, 'css-loader']
            },
            {
                test: /\.less$/,
                use: [
                    devMode ? 'style-loader' : MiniCssExtractPlugin.loader,
                    {
                        loader: 'css-loader',
                        options: {
                            modules: 'global'
                        }
                    },
                    {
                        loader: 'postcss-loader',
                        options: {
                            postcssOptions: {
                                plugins: [autoprefixer()]
                            }
                        }
                    },
                    {
                        loader: 'less-loader',
                        options: {
                            lessOptions: {
                                javascriptEnabled: true,
                                modules: true,
                                localIndexName: '[name]__[local]___[chunkhash:base64:5]',
                                modifyVars: {
                                    hack: 'true; @import "~tntd/themes/default/variables.less";'
                                }
                            }
                        }
                    }
                ]
            },
            {
                test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
                type: 'asset', // 方法2 asset/inline
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                },
                generator: {
                    filename: path.posix.join(staticSourcePath, 'img/[name].[hash:7][ext]')
                }
            },
            {
                test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
                type: 'asset', // 方法2 asset/inline
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                },
                generator: {
                    filename: path.posix.join(staticSourcePath, 'media/[name].[hash:7][ext]')
                }
            },
            {
                test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                type: 'asset', // 方法2 asset/inline
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                },
                generator: {
                    filename: path.posix.join(staticSourcePath, 'fonts/[name].[hash:7][ext]')
                }
            },
            {
                test: /\.(txt|jst)$/,
                loader: 'raw-loader'
            }
        ]
    }
};
